---
owner: "[TECHNICAL_LEAD]"
last_reviewed: "[YYYY-<PERSON>M-DD]"
status: "draft"
version: "1.0.0"
target_audience: "Junior developers with 6 months-2 years experience"
framework_version: "Laravel 12.x"
---

# Laravel 12.x Implementation Guide
## [PROJECT_NAME]

**Estimated Reading Time:** 20 minutes

## Overview

This guide provides Laravel 12.x specific implementation standards and patterns for [PROJECT_NAME]. It focuses on modern Laravel practices including the new `bootstrap/providers.php` pattern, database optimizations, and testing strategies.

## Service Provider Configuration

### Bootstrap Providers Pattern (Laravel 12.x)

**File**: `bootstrap/providers.php`
```php
<?php
// Modern Laravel 12.x service provider registration
return [
    App\Providers\AppServiceProvider::class,
    App\Providers\[PROJECT]ServiceProvider::class,
    App\Providers\FilamentServiceProvider::class,
    App\Providers\SqliteServiceProvider::class,
];
```

### Custom Service Provider Template

**File**: `app/Providers/[PROJECT]ServiceProvider.php`
```php
<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\[PROJECT]Service;

class [PROJECT]ServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton([PROJECT]Service::class);
        
        // Register configuration
        $this->mergeConfigFrom(
            __DIR__.'/../../config/[project].php', '[project]'
        );
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Publish configuration
        $this->publishes([
            __DIR__.'/../../config/[project].php' => config_path('[project].php'),
        ], '[project]-config');
        
        // Register Artisan commands
        if ($this->app->runningInConsole()) {
            $this->commands([
                \App\Console\Commands\[PROJECT]Command::class,
            ]);
        }
        
        // Register event listeners
        $this->registerEventListeners();
    }
    
    /**
     * Register event listeners for the service.
     */
    protected function registerEventListeners(): void
    {
        // Event listener registration
    }
}
```

## Database Implementation Standards

### Migration Template with Documentation

**File**: `database/migrations/[timestamp]_create_[table]_table.php`
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Creates [table] table for [purpose description].
     * Includes user stamps, soft deletes, and GDPR compliance features.
     */
    public function up(): void
    {
        Schema::create('[table]', function (Blueprint $table) {
            // Primary key using ULID
            $table->ulid('id')->primary();
            
            // Core fields
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('status', ['active', 'inactive', 'archived'])
                  ->default('active');
            
            // User stamps (wildside/userstamps)
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->unsignedBigInteger('deleted_by')->nullable();
            
            // Timestamps and soft deletes
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes for performance
            $table->index(['status', 'created_at']);
            $table->index('created_by');
        });
        
        // SQLite-specific optimizations
        if (config('database.default') === 'sqlite') {
            DB::statement('PRAGMA journal_mode=WAL');
            DB::statement('PRAGMA synchronous=NORMAL');
            DB::statement('PRAGMA cache_size=10000');
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('[table]');
    }
};
```

### Model Template with Best Practices

**File**: `app/Models/[MODEL].php`
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Wildside\Userstamps\Userstamps;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use App\Enums\[MODEL]Status;

class [MODEL] extends Model
{
    use HasFactory, SoftDeletes, Userstamps, LogsActivity;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'description',
        'status',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'status' => [MODEL]Status::class,
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Activity log configuration for audit trail.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['name', 'description', 'status'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Scope for filtering by status.
     */
    public function scopeByStatus($query, [MODEL]Status $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for active records.
     */
    public function scopeActive($query)
    {
        return $query->where('status', [MODEL]Status::Active);
    }
}
```

### Factory Template

**File**: `database/factories/[MODEL]Factory.php`
```php
<?php

namespace Database\Factories;

use App\Models\[MODEL];
use App\Enums\[MODEL]Status;
use Illuminate\Database\Eloquent\Factories\Factory;

class [MODEL]Factory extends Factory
{
    protected $model = [MODEL]::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->words(3, true),
            'description' => $this->faker->paragraph(),
            'status' => $this->faker->randomElement([MODEL]Status::cases()),
        ];
    }

    /**
     * Create active model.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => [MODEL]Status::Active,
        ]);
    }

    /**
     * Create inactive model.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => [MODEL]Status::Inactive,
        ]);
    }
}
```

## PHP 8.1+ Enum Implementation

### Status Enum Template

**File**: `app/Enums/[MODEL]Status.php`
```php
<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;
use Filament\Support\Contracts\HasColor;

enum [MODEL]Status: string implements HasLabel, HasColor
{
    case Active = 'active';
    case Inactive = 'inactive';
    case Archived = 'archived';

    public function getLabel(): ?string
    {
        return match($this) {
            self::Active => 'Active',
            self::Inactive => 'Inactive',
            self::Archived => 'Archived',
        };
    }

    public function getColor(): string|array|null
    {
        return match($this) {
            self::Active => 'success',
            self::Inactive => 'warning',
            self::Archived => 'danger',
        };
    }

    /**
     * Get all active statuses.
     */
    public static function activeStatuses(): array
    {
        return [self::Active];
    }

    /**
     * Check if status is active.
     */
    public function isActive(): bool
    {
        return $this === self::Active;
    }
}
```

## Testing Implementation

### Feature Test Template

**File**: `tests/Feature/[FEATURE]Test.php`
```php
<?php

namespace Tests\Feature;

use App\Models\[MODEL];
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class [FEATURE]Test extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create();
    }

    /** @test */
    public function authenticated_user_can_view_[feature]_index()
    {
        $this->actingAs($this->user)
            ->get(route('[feature].index'))
            ->assertOk()
            ->assertViewIs('[feature].index');
    }

    /** @test */
    public function authenticated_user_can_create_[feature]()
    {
        $data = [MODEL]::factory()->make()->toArray();

        $this->actingAs($this->user)
            ->post(route('[feature].store'), $data)
            ->assertRedirect(route('[feature].index'))
            ->assertSessionHas('success');

        $this->assertDatabaseHas('[table]', [
            'name' => $data['name'],
            'created_by' => $this->user->id,
        ]);
    }
}
```

## Performance Optimization

### SQLite Configuration

**File**: `app/Providers/SqliteServiceProvider.php`
```php
<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\DB;

class SqliteServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        if (config('database.default') === 'sqlite') {
            DB::statement('PRAGMA journal_mode=WAL');
            DB::statement('PRAGMA synchronous=NORMAL');
            DB::statement('PRAGMA cache_size=10000');
            DB::statement('PRAGMA temp_store=MEMORY');
            DB::statement('PRAGMA mmap_size=268435456'); // 256MB
        }
    }
}
```

### Query Optimization Guidelines

**Eager Loading Best Practices**:
```php
// Good: Eager load relationships
$users = User::with(['posts', 'comments'])->get();

// Bad: N+1 query problem
$users = User::all();
foreach ($users as $user) {
    echo $user->posts->count(); // N+1 queries
}
```

**Database Query Optimization**:
```php
// Use database-level operations when possible
User::where('status', 'inactive')
    ->where('last_login', '<', now()->subMonths(6))
    ->update(['status' => 'archived']);

// Instead of loading all records into memory
```

## Security Implementation

### Form Request Validation

**File**: `app/Http/Requests/Store[MODEL]Request.php`
```php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Enums\[MODEL]Status;
use Illuminate\Validation\Rules\Enum;

class Store[MODEL]Request extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->can('create', [MODEL]::class);
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1000'],
            'status' => ['required', new Enum([MODEL]Status::class)],
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'The name field is required.',
            'status.required' => 'Please select a valid status.',
        ];
    }
}
```

## Artisan Commands

### Custom Command Template

**File**: `app/Console/Commands/[PROJECT]Command.php`
```php
<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class [PROJECT]Command extends Command
{
    protected $signature = '[project]:{action} {--option=default}';
    protected $description = 'Description of the command';

    public function handle(): int
    {
        $this->info('Starting [PROJECT] command...');
        
        try {
            // Command logic here
            
            $this->info('Command completed successfully!');
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('Command failed: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
```

---

## Definition of Done Checklist

### Laravel Setup and Configuration
- [ ] Laravel 12.x installed with correct PHP version (8.1+)
- [ ] Service providers configured using bootstrap/providers.php pattern
- [ ] Environment configuration completed for all environments
- [ ] Database connection and SQLite optimizations configured
- [ ] Custom service providers created and registered

### Model and Database Implementation
- [ ] All models follow the template pattern with proper traits
- [ ] Migrations include ULID primary keys, user stamps, and soft deletes
- [ ] Database indexes created for performance optimization
- [ ] Factories created for all models with realistic test data
- [ ] Enums implemented using PHP 8.1+ enum syntax with Filament integration

### Security Implementation
- [ ] Form request validation implemented for all user inputs
- [ ] Authorization policies created and tested
- [ ] CSRF protection enabled for all forms
- [ ] Input sanitization implemented to prevent XSS
- [ ] Activity logging configured for audit trails

### Testing Implementation
- [ ] Unit tests written for all models and services (90%+ coverage)
- [ ] Feature tests created for all user workflows
- [ ] Test database properly isolated using RefreshDatabase trait
- [ ] Factory patterns used for test data generation
- [ ] Performance tests included for critical operations

### Performance and Optimization
- [ ] SQLite WAL mode and optimizations configured
- [ ] Eager loading implemented to prevent N+1 queries
- [ ] Database queries optimized with proper indexing
- [ ] Caching strategies implemented where appropriate
- [ ] Performance benchmarks established and met

### Code Quality and Standards
- [ ] Code follows Laravel conventions and PSR standards
- [ ] All classes properly documented with PHPDoc
- [ ] Error handling implemented with appropriate exception types
- [ ] Logging configured for debugging and monitoring
- [ ] Code review completed and approved

---

## Common Pitfalls and Avoidance Strategies

### Pitfall: Incorrect Service Provider Registration
**Problem**: Using old Laravel patterns instead of Laravel 12.x bootstrap/providers.php
**Solution**: Use the new bootstrap/providers.php pattern for service provider registration
**Example**: Register providers in bootstrap/providers.php instead of config/app.php

### Pitfall: Poor Database Performance
**Problem**: Not optimizing SQLite configuration or missing database indexes
**Solution**: Implement SQLite optimizations and create proper indexes
**Example**: Use WAL mode, add indexes for frequently queried columns, implement eager loading

### Pitfall: Inadequate Model Relationships
**Problem**: Missing or incorrectly defined Eloquent relationships
**Solution**: Define all relationships properly with appropriate foreign key constraints
**Example**: Use proper belongsTo, hasMany, and belongsToMany relationships with foreign key definitions

### Pitfall: Insufficient Input Validation
**Problem**: Relying only on client-side validation or basic Laravel validation
**Solution**: Implement comprehensive Form Request validation with custom rules
**Example**: Create dedicated Form Request classes with authorization and validation logic

### Pitfall: Missing Audit Trail
**Problem**: Not tracking changes to important data for compliance and debugging
**Solution**: Implement activity logging using spatie/laravel-activitylog
**Example**: Configure LogsActivity trait on models that need audit trails

### Pitfall: Inefficient Testing Patterns
**Problem**: Slow tests due to poor database handling or missing factories
**Solution**: Use proper test isolation and factory patterns
**Example**: Use RefreshDatabase trait, create comprehensive factories, avoid real external API calls

---

**Implementation Guide Version**: 1.0.0
**Framework Version**: Laravel 12.x
**Last Updated**: [YYYY-MM-DD]
**Next Review**: [YYYY-MM-DD]
