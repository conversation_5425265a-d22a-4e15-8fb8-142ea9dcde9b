<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add slug field for SEO-friendly URLs (nullable initially)
            if (!Schema::hasColumn('users', 'slug')) {
                $table->string('slug')->nullable()->unique()->after('name');
            }

            // Add public_id field for external references (ULID) (nullable initially)
            if (!Schema::hasColumn('users', 'public_id')) {
                $table->string('public_id', 26)->nullable()->unique()->after('id');
            }

            // Add soft deletes support
            if (!Schema::hasColumn('users', 'deleted_at')) {
                $table->softDeletes();
            }

            // Add userstamps fields for audit trail
            if (!Schema::hasColumn('users', 'created_by')) {
                $table->unsignedBigInteger('created_by')->nullable()->after('created_at');
            }
            if (!Schema::hasColumn('users', 'updated_by')) {
                $table->unsignedBigInteger('updated_by')->nullable()->after('updated_at');
            }
            if (!Schema::hasColumn('users', 'deleted_by')) {
                $table->unsignedBigInteger('deleted_by')->nullable()->after('deleted_at');
            }
        });

        // Add indexes and foreign keys in a separate schema call to avoid conflicts
        Schema::table('users', function (Blueprint $table) {
            // Add indexes for performance (with try-catch to handle existing indexes)
            try {
                $table->index('slug');
            } catch (Exception $e) {
                // Index already exists, ignore
            }

            try {
                $table->index('public_id');
            } catch (Exception $e) {
                // Index already exists, ignore
            }

            try {
                $table->index('created_by');
            } catch (Exception $e) {
                // Index already exists, ignore
            }

            try {
                $table->index('updated_by');
            } catch (Exception $e) {
                // Index already exists, ignore
            }

            try {
                $table->index('deleted_by');
            } catch (Exception $e) {
                // Index already exists, ignore
            }

            // Add foreign key constraints for userstamps (with try-catch to handle existing constraints)
            try {
                $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            } catch (Exception $e) {
                // Foreign key already exists, ignore
            }

            try {
                $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');
            } catch (Exception $e) {
                // Foreign key already exists, ignore
            }

            try {
                $table->foreign('deleted_by')->references('id')->on('users')->onDelete('set null');
            } catch (Exception $e) {
                // Foreign key already exists, ignore
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Use a completely resilient approach - wrap everything in try-catch
        try {
            // Drop foreign key constraints first
            Schema::table('users', function (Blueprint $table) {
                if (Schema::hasColumn('users', 'created_by')) {
                    try { $table->dropForeign(['created_by']); } catch (Exception $e) { /* ignore */ }
                }
                if (Schema::hasColumn('users', 'updated_by')) {
                    try { $table->dropForeign(['updated_by']); } catch (Exception $e) { /* ignore */ }
                }
                if (Schema::hasColumn('users', 'deleted_by')) {
                    try { $table->dropForeign(['deleted_by']); } catch (Exception $e) { /* ignore */ }
                }
            });
        } catch (Exception $e) {
            // Continue even if foreign key dropping fails
        }

        try {
            // Drop indexes - try multiple naming conventions
            Schema::table('users', function (Blueprint $table) {
                $columns = ['slug', 'public_id', 'created_by', 'updated_by', 'deleted_by'];

                foreach ($columns as $column) {
                    if (Schema::hasColumn('users', $column)) {
                        // Try different index naming patterns
                        $indexNames = [
                            $column,
                            'users_' . $column . '_index',
                            'users_' . $column . '_unique',
                        ];

                        foreach ($indexNames as $indexName) {
                            try {
                                $table->dropIndex([$indexName]);
                                break; // If successful, move to next column
                            } catch (Exception $e) {
                                continue; // Try next naming pattern
                            }
                        }
                    }
                }
            });
        } catch (Exception $e) {
            // Continue even if index dropping fails
        }

        try {
            // Drop columns - this is the most important part
            Schema::table('users', function (Blueprint $table) {
                $columnsToDrop = [];

                if (Schema::hasColumn('users', 'slug')) $columnsToDrop[] = 'slug';
                if (Schema::hasColumn('users', 'public_id')) $columnsToDrop[] = 'public_id';
                if (Schema::hasColumn('users', 'deleted_at')) $columnsToDrop[] = 'deleted_at';
                if (Schema::hasColumn('users', 'created_by')) $columnsToDrop[] = 'created_by';
                if (Schema::hasColumn('users', 'updated_by')) $columnsToDrop[] = 'updated_by';
                if (Schema::hasColumn('users', 'deleted_by')) $columnsToDrop[] = 'deleted_by';

                if (!empty($columnsToDrop)) {
                    $table->dropColumn($columnsToDrop);
                }
            });
        } catch (Exception $e) {
            // If bulk drop fails, try dropping columns individually
            $columns = ['slug', 'public_id', 'deleted_at', 'created_by', 'updated_by', 'deleted_by'];
            foreach ($columns as $column) {
                try {
                    if (Schema::hasColumn('users', $column)) {
                        Schema::table('users', function (Blueprint $table) use ($column) {
                            $table->dropColumn($column);
                        });
                    }
                } catch (Exception $e) {
                    // Continue to next column
                    continue;
                }
            }
        }
    }
};
