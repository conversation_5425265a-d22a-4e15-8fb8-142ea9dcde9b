<?php declare(strict_types = 1);

// odsl-/Users/<USER>/Herd/lfsl/app
return \PHPStan\Cache\CacheItem::__set_state(array(
   'variableKey' => 'v1',
   'data' => 
  array (
    '/Users/<USER>/Herd/lfsl/app/Providers/AppServiceProvider.php' => 
    array (
      0 => '3feca275b10402f9110b22b5d6b2cf62b07653ce',
      1 => 
      array (
        0 => 'app\\providers\\appserviceprovider',
      ),
      2 => 
      array (
        0 => 'app\\providers\\register',
        1 => 'app\\providers\\boot',
        2 => 'app\\providers\\configurecarbon',
        3 => 'app\\providers\\configurecommands',
        4 => 'app\\providers\\configuremodels',
        5 => 'app\\providers\\configurepassworddefaults',
        6 => 'app\\providers\\configureurl',
        7 => 'app\\providers\\configurevite',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/lfsl/app/Providers/VoltServiceProvider.php' => 
    array (
      0 => '0d0bf85e1bb1544d1d10558972fc8126ef72b5b1',
      1 => 
      array (
        0 => 'app\\providers\\voltserviceprovider',
      ),
      2 => 
      array (
        0 => 'app\\providers\\register',
        1 => 'app\\providers\\boot',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/lfsl/app/Models/User.php' => 
    array (
      0 => 'efbf10fd33f43633807a1c8e96e1eadca5a31a03',
      1 => 
      array (
        0 => 'app\\models\\user',
      ),
      2 => 
      array (
        0 => 'app\\models\\casts',
        1 => 'app\\models\\boot',
        2 => 'app\\models\\getroutekeyname',
        3 => 'app\\models\\getslugoptions',
        4 => 'app\\models\\getactivitylogoptions',
        5 => 'app\\models\\canaccesspanel',
        6 => 'app\\models\\getfilamentavatarurl',
        7 => 'app\\models\\initials',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/lfsl/app/Livewire/Actions/Logout.php' => 
    array (
      0 => 'c23deb662e98fca0af0791b97dcedb39f0b60ce2',
      1 => 
      array (
        0 => 'app\\livewire\\actions\\logout',
      ),
      2 => 
      array (
        0 => 'app\\livewire\\actions\\__invoke',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/lfsl/app/Http/Controllers/Controller.php' => 
    array (
      0 => 'a33a5105f92c73a309c9f8a549905dcdf6dccbae',
      1 => 
      array (
        0 => 'app\\http\\controllers\\controller',
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/lfsl/app/Http/Controllers/Auth/VerifyEmailController.php' => 
    array (
      0 => '05d712c6d9f692c553ceb0c04dcdf34ab65d6d91',
      1 => 
      array (
        0 => 'app\\http\\controllers\\auth\\verifyemailcontroller',
      ),
      2 => 
      array (
        0 => 'app\\http\\controllers\\auth\\__invoke',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/lfsl/app/Providers/Filament/AdminPanelProvider.php' => 
    array (
      0 => '883c4e9064ef2f56ceccee75803f83fff66a0b96',
      1 => 
      array (
        0 => 'app\\providers\\filament\\adminpanelprovider',
      ),
      2 => 
      array (
        0 => 'app\\providers\\filament\\panel',
      ),
      3 => 
      array (
      ),
    ),
  ),
));